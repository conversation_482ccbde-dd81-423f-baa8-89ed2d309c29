'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { MessageSquare, Send, User, Mail, Globe } from 'lucide-react';
import { toast } from '@/lib/toast';

interface CommentFormProps {
  postId: string;
  parentId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
  isReply?: boolean;
}

export default function CommentForm({ 
  postId, 
  parentId, 
  onSuccess, 
  onCancel, 
  isReply = false 
}: CommentFormProps) {
  const [formData, setFormData] = useState({
    author_name: '',
    author_email: '',
    author_website: '',
    content: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          post: postId,
          parent: parentId || null,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('评论提交成功！等待审核后显示。');
        
        // 重置表单
        setFormData({
          author_name: '',
          author_email: '',
          author_website: '',
          content: ''
        });
        
        if (onSuccess) {
          onSuccess();
        }
      } else {
        setError(data.error || '提交评论失败');
      }
    } catch (error) {
      console.error('Comment submission error:', error);
      setError('网络错误，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError('');
  };

  return (
    <Card className={isReply ? 'ml-8 mt-4' : 'mt-8'}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MessageSquare className="h-5 w-5" />
          <span>{isReply ? '回复评论' : '发表评论'}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 姓名和邮箱 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="author_name">姓名 *</Label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User className="h-4 w-4 text-gray-400" />
                </div>
                <Input
                  id="author_name"
                  type="text"
                  value={formData.author_name}
                  onChange={(e) => handleInputChange('author_name', e.target.value)}
                  placeholder="您的姓名"
                  className="pl-10"
                  required
                  disabled={isLoading}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="author_email">邮箱 *</Label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-4 w-4 text-gray-400" />
                </div>
                <Input
                  id="author_email"
                  type="email"
                  value={formData.author_email}
                  onChange={(e) => handleInputChange('author_email', e.target.value)}
                  placeholder="您的邮箱（不会公开显示）"
                  className="pl-10"
                  required
                  disabled={isLoading}
                />
              </div>
            </div>
          </div>

          {/* 网站（可选） */}
          <div>
            <Label htmlFor="author_website">网站（可选）</Label>
            <div className="mt-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Globe className="h-4 w-4 text-gray-400" />
              </div>
              <Input
                id="author_website"
                type="url"
                value={formData.author_website}
                onChange={(e) => handleInputChange('author_website', e.target.value)}
                placeholder="https://yourwebsite.com"
                className="pl-10"
                disabled={isLoading}
              />
            </div>
          </div>

          {/* 评论内容 */}
          <div>
            <Label htmlFor="content">评论内容 *</Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) => handleInputChange('content', e.target.value)}
              placeholder={isReply ? "写下您的回复..." : "写下您的评论..."}
              rows={4}
              className="mt-1"
              required
              disabled={isLoading}
            />
          </div>

          {/* 提交按钮 */}
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-500">
              评论将在审核后显示
            </p>
            
            <div className="flex space-x-3">
              {isReply && onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading}
                >
                  取消
                </Button>
              )}
              
              <Button
                type="submit"
                disabled={isLoading || !formData.author_name || !formData.author_email || !formData.content}
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    提交中...
                  </div>
                ) : (
                  <div className="flex items-center">
                    <Send className="h-4 w-4 mr-2" />
                    {isReply ? '回复' : '发表评论'}
                  </div>
                )}
              </Button>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
