# Enhanced Error Handling System

This document describes the comprehensive error handling system implemented in this Directus Next.js application. The system provides detailed error logging, retry mechanisms, and user-friendly error reporting for all API requests.

## 🚨 Key Features

- **Detailed Error Logging**: Every failed request is logged with comprehensive information
- **Automatic Retry Logic**: Configurable retry mechanisms for transient failures
- **Request Tracking**: Unique request IDs for tracking and debugging
- **Development Monitoring**: Real-time error monitoring dashboard in development
- **User-Friendly Error Boundaries**: Graceful error handling in React components
- **Structured Error Responses**: Consistent error format across the application

## 📁 File Structure

```
src/
├── lib/
│   ├── error-handler.ts          # Core error handling utilities
│   └── directus/
│       ├── directus.ts           # Enhanced Directus client with error handling
│       └── fetchers.ts           # API fetchers with retry logic
├── components/
│   ├── error-boundary.tsx        # React error boundary component
│   └── error-monitor.tsx         # Development error monitoring dashboard
├── hooks/
│   └── useApiError.ts            # React hooks for API error handling
└── app/
    └── api/
        └── search/
            └── route.ts          # Example API route with error handling
```

## 🔧 Core Components

### 1. <PERSON><PERSON><PERSON> (`src/lib/error-handler.ts`)

The central error handling utility that provides:

- **Request ID Generation**: Unique identifiers for tracking requests
- **Detailed Error Logging**: Comprehensive error information with context
- **Enhanced Fetch Wrapper**: Fetch with built-in error handling
- **Retry Logic**: Configurable retry mechanisms

```typescript
import { logRequestError, createApiError, retryWithErrorHandling } from '@/lib/error-handler';

// Log an error with detailed context
const errorDetails = logRequestError(error, {
  operation: 'fetchUserData',
  url: '/api/users/123',
  method: 'GET',
  requestData: { userId: 123 }
});

// Retry an operation with error handling
const result = await retryWithErrorHandling(
  () => fetchUserData(),
  {
    maxRetries: 3,
    delay: 1000,
    operationName: 'fetchUserData',
    shouldRetry: (error) => error.status >= 500
  }
);
```

### 2. Enhanced Directus Client (`src/lib/directus/directus.ts`)

The Directus client is enhanced with:

- **Automatic Error Logging**: All failed requests are logged
- **Rate Limit Handling**: Automatic retry for 429 responses
- **Request Context**: Detailed information about each request

### 3. Error Boundary (`src/components/error-boundary.tsx`)

React error boundary that:

- **Catches React Errors**: Prevents app crashes
- **Logs Component Errors**: Detailed error information
- **Provides Fallback UI**: User-friendly error display
- **Retry Functionality**: Allow users to retry failed operations

```tsx
import { ErrorBoundary, withErrorBoundary } from '@/components/error-boundary';

// Wrap components with error boundary
export default withErrorBoundary(MyComponent);

// Or use directly
<ErrorBoundary fallback={CustomErrorComponent}>
  <MyComponent />
</ErrorBoundary>
```

### 4. API Error Hooks (`src/hooks/useApiError.ts`)

React hooks for handling API errors:

```tsx
import { useApiError, useFetchWithError, useSearchWithError } from '@/hooks/useApiError';

function MyComponent() {
  const { isError, error, retry, executeWithErrorHandling } = useApiError();
  
  const handleSearch = async (query: string) => {
    const results = await executeWithErrorHandling(
      () => searchAPI(query),
      { operationName: 'search', requestData: { query } }
    );
  };
}
```

## 📊 Error Information

When a request fails, the following information is logged:

```typescript
interface ErrorDetails {
  message: string;           // Error message
  status?: number;          // HTTP status code
  statusText?: string;      // HTTP status text
  url?: string;            // Request URL
  method?: string;         // HTTP method
  timestamp: string;       // ISO timestamp
  requestId: string;       // Unique request identifier
  stack?: string;          // Error stack trace
  directusError?: any;     // Directus-specific error details
}
```

## 🔄 Retry Logic

The system includes intelligent retry logic:

- **Automatic Retries**: For 5xx errors and 429 (rate limiting)
- **Configurable Delays**: Customizable retry delays
- **Max Retry Limits**: Prevent infinite retry loops
- **Custom Retry Conditions**: Define when to retry based on error type

## 🛠️ Development Tools

### Error Monitor Dashboard

In development mode, a real-time error monitoring dashboard is available:

- **Error Count Badge**: Shows total number of errors
- **Detailed Error List**: View all errors with context
- **Filtering Options**: Filter by error type (4xx, 5xx, network)
- **Request Tracking**: Track errors by request ID

### Console Logging

All errors are logged to the console with detailed information:

```
🚨 API Request Failed - fetchPageData(/about)
Request ID: req_1234567890_abc123def
Timestamp: 2024-01-15T10:30:00.000Z
URL: https://directus.example.com/items/pages
Method: GET
Status: 500 Internal Server Error
Message: Database connection failed
Request Data: { filter: { permalink: { _eq: "/about" } } }
Directus Error Details: { ... }
Stack Trace: Error: Database connection failed...
```

## 🎯 Usage Examples

### Basic API Call with Error Handling

```typescript
import { retryWithErrorHandling } from '@/lib/error-handler';

const fetchUserProfile = async (userId: string) => {
  return retryWithErrorHandling(
    async () => {
      const response = await fetch(`/api/users/${userId}`);
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      return response.json();
    },
    {
      maxRetries: 3,
      delay: 1000,
      operationName: `fetchUserProfile(${userId})`,
      shouldRetry: (error) => error.status >= 500
    }
  );
};
```

### React Component with Error Handling

```tsx
import { useApiError } from '@/hooks/useApiError';
import { ErrorBoundary } from '@/components/error-boundary';

function UserProfile({ userId }: { userId: string }) {
  const [user, setUser] = useState(null);
  const { isError, error, isLoading, executeWithErrorHandling } = useApiError();

  useEffect(() => {
    executeWithErrorHandling(
      () => fetchUserProfile(userId),
      { operationName: 'fetchUserProfile', requestData: { userId } }
    ).then(setUser);
  }, [userId]);

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error: {error?.message}</div>;
  
  return <div>{user?.name}</div>;
}

export default withErrorBoundary(UserProfile);
```

### API Route with Error Handling

```typescript
import { logRequestError } from '@/lib/error-handler';

export async function GET(request: Request) {
  try {
    const data = await fetchSomeData();
    return NextResponse.json(data);
  } catch (error) {
    logRequestError(error, {
      operation: 'API Route Handler',
      url: request.url,
      method: 'GET'
    });

    return NextResponse.json(
      { 
        error: 'Internal server error',
        requestId: (error as any)?.details?.requestId 
      },
      { status: 500 }
    );
  }
}
```

## 🔧 Configuration

### Environment Variables

The error handling system respects the following environment variables:

- `NODE_ENV`: Controls development features (error monitor, detailed logging)
- `NEXT_PUBLIC_DIRECTUS_URL`: Directus instance URL for error context

### Customization

You can customize the error handling behavior by:

1. **Modifying Retry Logic**: Adjust retry conditions in `error-handler.ts`
2. **Custom Error UI**: Create custom fallback components for error boundaries
3. **Error Reporting**: Integrate with external error reporting services
4. **Logging Levels**: Adjust console logging verbosity

## 🚀 Best Practices

1. **Always Use Error Boundaries**: Wrap components that make API calls
2. **Provide Retry Options**: Allow users to retry failed operations
3. **Log Context Information**: Include relevant data with error logs
4. **Handle Different Error Types**: Distinguish between client and server errors
5. **Monitor in Development**: Use the error monitor dashboard during development
6. **Graceful Degradation**: Provide fallback content when possible

## 🐛 Debugging

When debugging API errors:

1. **Check the Console**: Look for detailed error logs
2. **Use Request IDs**: Track specific requests across logs
3. **Monitor Dashboard**: Use the development error monitor
4. **Check Network Tab**: Verify actual HTTP requests and responses
5. **Review Error Context**: Check the logged request data and context

This error handling system ensures that every failed request provides detailed information for debugging while maintaining a good user experience through graceful error handling and retry mechanisms.
