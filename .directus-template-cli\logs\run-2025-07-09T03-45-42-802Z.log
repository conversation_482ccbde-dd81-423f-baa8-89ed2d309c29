Log started at 2025-07-09T03-45-42-802Z
2025-07-09T03:50:06.447Z - ERROR: Directus Error: Value for field "id" in collection "directus_extensions" has to be unique. (Status: 400) {"collection":"directus_extensions","field":"id","code":"RECORD_NOT_UNIQUE"} Context: {}
2025-07-09T03:50:06.763Z - ERROR: Directus Error: Value for field "id" in collection "directus_extensions" has to be unique. (Status: 400) {"collection":"directus_extensions","field":"id","code":"RECORD_NOT_UNIQUE"} Context: {}
2025-07-09T03:50:07.456Z - ERROR: Directus Error: Value for field "id" in collection "directus_extensions" has to be unique. (Status: 400) {"collection":"directus_extensions","field":"id","code":"RECORD_NOT_UNIQUE"} Context: {}
2025-07-09T03:50:07.625Z - ERROR: Directus Error: Value for field "id" in collection "directus_extensions" has to be unique. (Status: 400) {"collection":"directus_extensions","field":"id","code":"RECORD_NOT_UNIQUE"} Context: {}
2025-07-09T03:50:07.826Z - ERROR: Directus Error: Value for field "id" in collection "directus_extensions" has to be unique. (Status: 400) {"collection":"directus_extensions","field":"id","code":"RECORD_NOT_UNIQUE"} Context: {}
2025-07-09T03:50:08.071Z - ERROR: Directus Error: Value for field "id" in collection "directus_extensions" has to be unique. (Status: 400) {"collection":"directus_extensions","field":"id","code":"RECORD_NOT_UNIQUE"} Context: {}
2025-07-09T03:50:08.205Z - ERROR: Directus Error: Value for field "id" in collection "directus_extensions" has to be unique. (Status: 400) {"collection":"directus_extensions","field":"id","code":"RECORD_NOT_UNIQUE"} Context: {}
2025-07-09T03:50:08.271Z - ERROR: Directus Error: Value for field "id" in collection "directus_extensions" has to be unique. (Status: 400) {"collection":"directus_extensions","field":"id","code":"RECORD_NOT_UNIQUE"} Context: {}
2025-07-09T03:50:08.470Z - ERROR: Directus Error: Value for field "id" in collection "directus_extensions" has to be unique. (Status: 400) {"collection":"directus_extensions","field":"id","code":"RECORD_NOT_UNIQUE"} Context: {}
2025-07-09T03:50:09.054Z - ERROR: Directus Error: Value for field "id" in collection "directus_extensions" has to be unique. (Status: 400) {"collection":"directus_extensions","field":"id","code":"RECORD_NOT_UNIQUE"} Context: {}
2025-07-09T03:50:14.879Z - ERROR: Directus Error: Value for field "id" in collection "directus_extensions" has to be unique. (Status: 400) {"collection":"directus_extensions","field":"id","code":"RECORD_NOT_UNIQUE"} Context: {}
2025-07-09T03:50:15.540Z - ERROR: Directus Error: Value for field "id" in collection "directus_extensions" has to be unique. (Status: 400) {"collection":"directus_extensions","field":"id","code":"RECORD_NOT_UNIQUE"} Context: {}
