'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminLayout from '@/components/admin/AdminLayout';
import BlogEditor from '@/components/admin/BlogEditor';
import { Post } from '@/types/directus-schema';
import { toast } from '@/lib/toast';

interface EditBlogPageProps {
  params: Promise<{ id: string }>;
}

export default function EditBlogPage({ params }: EditBlogPageProps) {
  const router = useRouter();
  const [post, setPost] = useState<Post | null>(null);
  const [loading, setLoading] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [postId, setPostId] = useState<string>('');

  // 获取博客文章数据
  const fetchPost = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/blog/${id}`);
      if (response.ok) {
        const data = await response.json();
        setPost(data);
      } else {
        throw new Error('Post not found');
      }
    } catch (error) {
      console.error('Error fetching post:', error);
      toast.error('获取博客数据失败');
      router.push('/admin/blog');
    } finally {
      setLoading(false);
    }
  };

  // 保存博客
  const handleSave = async (postData: Partial<Post>) => {
    setIsLoading(true);

    try {
      const response = await fetch(`/api/admin/blog/${postId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(postData),
      });

      if (response.ok) {
        const updatedPost = await response.json();
        setPost(updatedPost);
        toast.success(
          postData.status === 'published'
            ? '博客发布成功！'
            : '博客保存成功！'
        );

        // 如果发布了，跳转到博客页面
        if (postData.status === 'published') {
          router.push(`/blog/${updatedPost.slug}`);
        }
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update post');
      }
    } catch (error) {
      console.error('Error updating post:', error);
      toast.error('保存博客失败：' + (error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePreview = (postData: Partial<Post>) => {
    // 在新窗口中打开预览
    if (postData.slug) {
      window.open(`/blog/${postData.slug}`, '_blank');
    } else {
      toast.info('请先保存博客以生成预览链接');
    }
  };

  useEffect(() => {
    const initPage = async () => {
      const resolvedParams = await params;
      setPostId(resolvedParams.id);
      await fetchPost(resolvedParams.id);
    };

    initPage();
  }, [params]);

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">加载中...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (!post) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">博客不存在</h2>
          <p className="text-gray-600 mb-6">找不到指定的博客文章</p>
          <button
            onClick={() => router.push('/admin/blog')}
            className="text-blue-600 hover:text-blue-800"
          >
            返回博客列表
          </button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <BlogEditor
        post={post}
        onSave={handleSave}
        onPreview={handlePreview}
        isLoading={isLoading}
      />
    </AdminLayout>
  );
}
