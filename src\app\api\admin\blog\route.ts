import { NextRequest, NextResponse } from 'next/server';
import { useDirectus } from '@/lib/directus/directus';
import { readItems, createItem, updateItem, deleteItem } from '@directus/sdk';

// 获取所有博客文章
export async function GET() {
  try {
    const { directus } = useDirectus();
    
    const posts = await directus.request(
      readItems('posts', {
        fields: [
          'id',
          'title',
          'slug',
          'description',
          'status',
          'date_created',
          'date_updated',
          'published_at',
          { author: ['id', 'first_name', 'last_name'] }
        ],
        sort: ['-date_updated'],
        limit: -1, // 获取所有文章
      })
    );

    return NextResponse.json(posts);
  } catch (error) {
    console.error('Error fetching posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch posts' },
      { status: 500 }
    );
  }
}

// 创建新博客文章
export async function POST(request: NextRequest) {
  try {
    const { directus } = useDirectus();
    const body = await request.json();
    
    // 验证必填字段
    if (!body.title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      );
    }

    // 生成 slug（如果没有提供）
    if (!body.slug) {
      body.slug = body.title
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .trim();
    }

    // 设置发布时间
    if (body.status === 'published' && !body.published_at) {
      body.published_at = new Date().toISOString();
    }

    const newPost = await directus.request(
      createItem('posts', {
        title: body.title,
        slug: body.slug,
        description: body.description,
        content: body.content,
        status: body.status || 'draft',
        published_at: body.published_at,
        // 这里可以添加当前用户作为作者
        // author: currentUserId,
      })
    );

    return NextResponse.json(newPost, { status: 201 });
  } catch (error) {
    console.error('Error creating post:', error);
    return NextResponse.json(
      { error: 'Failed to create post' },
      { status: 500 }
    );
  }
}
