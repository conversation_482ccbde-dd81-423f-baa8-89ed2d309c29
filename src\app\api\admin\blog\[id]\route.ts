import { NextRequest, NextResponse } from 'next/server';
import { useDirectus } from '@/lib/directus/directus';
import { readItem, updateItem, deleteItem } from '@directus/sdk';

// 获取单个博客文章
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { directus } = useDirectus();
    
    const post = await directus.request(
      readItem('posts', id, {
        fields: [
          'id',
          'title',
          'slug',
          'description',
          'content',
          'status',
          'image',
          'published_at',
          'date_created',
          'date_updated',
          { author: ['id', 'first_name', 'last_name'] }
        ]
      })
    );

    return NextResponse.json(post);
  } catch (error) {
    console.error('Error fetching post:', error);
    return NextResponse.json(
      { error: 'Post not found' },
      { status: 404 }
    );
  }
}

// 更新博客文章
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { directus } = useDirectus();
    const body = await request.json();
    
    // 验证必填字段
    if (!body.title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      );
    }

    // 如果状态改为已发布且没有发布时间，设置发布时间
    if (body.status === 'published' && !body.published_at) {
      body.published_at = new Date().toISOString();
    }

    const updatedPost = await directus.request(
      updateItem('posts', id, {
        title: body.title,
        slug: body.slug,
        description: body.description,
        content: body.content,
        status: body.status,
        published_at: body.published_at,
        image: body.image,
      })
    );

    return NextResponse.json(updatedPost);
  } catch (error) {
    console.error('Error updating post:', error);
    return NextResponse.json(
      { error: 'Failed to update post' },
      { status: 500 }
    );
  }
}

// 删除博客文章
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { directus } = useDirectus();
    
    await directus.request(deleteItem('posts', id));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting post:', error);
    return NextResponse.json(
      { error: 'Failed to delete post' },
      { status: 500 }
    );
  }
}
